# PerplexiGate Deployment Guide

## 🚀 **Step-by-Step Deployment**

### **Option 1: Local Development (Easiest)**

#### **Step 1: Prerequisites**
```bash
# Install Docker and Docker Compose
# Ubuntu/Debian:
sudo apt update && sudo apt install docker.io docker-compose

# macOS:
brew install docker docker-compose

# Windows: Install Docker Desktop from docker.com
```

#### **Step 2: Setup Project**
```bash
# Clone repository
git clone <your-repository-url>
cd BellingByte-PerplexiGate

# Generate secure keys
SECRET_KEY=$(openssl rand -hex 32)
API_KEY=$(openssl rand -hex 16)

# Create environment file
cat > .env << EOF
# Security
SECRET_KEY=${SECRET_KEY}
DEFAULT_API_KEY=${API_KEY}

# Database
DATABASE_URL=************************************************/perplexigate

# Redis
REDIS_URL=redis://redis:6379/0

# Browser Configuration
BROWSER_POOL_SIZE=5
HEADLESS_MODE=true
ENABLE_SCREENSHOTS=true

# Performance
API_WORKERS=4
CELERY_WORKERS=4
MAX_CONCURRENT_REQUESTS=50

# Monitoring
ENABLE_METRICS=true
LOG_LEVEL=INFO
EOF

echo "Your API Key: ${API_KEY}"
```

#### **Step 3: Deploy**
```bash
# Start all services
./scripts/start.sh

# Or manually:
docker-compose up -d

# Check status
docker-compose ps
```

#### **Step 4: Test**
```bash
# Health check
curl http://localhost:8000/health

# Submit test query
curl -X POST "http://localhost:8000/api/v1/query" \
  -H "Authorization: Bearer ${API_KEY}" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is machine learning?",
    "search_mode": "quick"
  }'

# Check task status (replace TASK_ID with returned task_id)
curl -H "Authorization: Bearer ${API_KEY}" \
  http://localhost:8000/api/v1/query/TASK_ID
```

### **Option 2: Production Server (VPS/Cloud)**

#### **Step 1: Server Setup**
```bash
# Connect to your server
ssh <EMAIL>

# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Logout and login again for group changes
exit
ssh <EMAIL>
```

#### **Step 2: Deploy Application**
```bash
# Clone repository
git clone <your-repository-url>
cd BellingByte-PerplexiGate

# Create production environment
cat > .env << EOF
# Security
SECRET_KEY=$(openssl rand -hex 32)
DEFAULT_API_KEY=$(openssl rand -hex 16)

# Database
DATABASE_URL=postgresql://perplexigate:$(openssl rand -hex 16)@postgres:5432/perplexigate

# Redis
REDIS_URL=redis://redis:6379/0

# Production settings
DEBUG=false
HEADLESS_MODE=true
BROWSER_POOL_SIZE=8
API_WORKERS=6
CELERY_WORKERS=8

# Performance
MAX_CONCURRENT_REQUESTS=100
CONNECTION_POOL_SIZE=200
TASK_TIMEOUT=180

# Monitoring
ENABLE_METRICS=true
LOG_LEVEL=INFO
EOF

# Create production override
cat > docker-compose.override.yml << EOF
version: '3.8'
services:
  api:
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
  
  worker:
    restart: unless-stopped
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
  
  postgres:
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
  
  redis:
    restart: unless-stopped
    command: redis-server --appendonly yes
EOF

# Start services
docker-compose up -d

# Check logs
docker-compose logs -f
```

#### **Step 3: Setup Nginx (Optional)**
```bash
# Install Nginx
sudo apt install nginx

# Create configuration
sudo tee /etc/nginx/sites-available/perplexigate << EOF
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_timeout 300s;
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/perplexigate /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# Setup SSL with Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### **Option 3: Kubernetes Deployment**

#### **Step 1: Create Kubernetes Manifests**
```bash
mkdir -p k8s

# Namespace
cat > k8s/namespace.yaml << EOF
apiVersion: v1
kind: Namespace
metadata:
  name: perplexigate
EOF

# ConfigMap
cat > k8s/configmap.yaml << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: perplexigate-config
  namespace: perplexigate
data:
  DATABASE_URL: "************************************************/perplexigate"
  REDIS_URL: "redis://redis:6379/0"
  BROWSER_POOL_SIZE: "8"
  HEADLESS_MODE: "true"
  API_WORKERS: "4"
  CELERY_WORKERS: "6"
  ENABLE_METRICS: "true"
  LOG_LEVEL: "INFO"
EOF

# Secrets
cat > k8s/secrets.yaml << EOF
apiVersion: v1
kind: Secret
metadata:
  name: perplexigate-secrets
  namespace: perplexigate
type: Opaque
data:
  SECRET_KEY: $(echo -n "$(openssl rand -hex 32)" | base64)
  DEFAULT_API_KEY: $(echo -n "$(openssl rand -hex 16)" | base64)
EOF
```

#### **Step 2: Deploy to Kubernetes**
```bash
# Apply manifests
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml

# Deploy PostgreSQL
kubectl apply -f - << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: perplexigate
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: perplexigate
        - name: POSTGRES_USER
          value: perplexigate
        - name: POSTGRES_PASSWORD
          value: password
        ports:
        - containerPort: 5432
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: perplexigate
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
EOF

# Deploy Redis
kubectl apply -f - << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: perplexigate
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
---
apiVersion: v1
kind: Service
metadata:
  name: redis
  namespace: perplexigate
spec:
  selector:
    app: redis
  ports:
  - port: 6379
EOF

# Deploy API
kubectl apply -f - << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: perplexigate-api
  namespace: perplexigate
spec:
  replicas: 3
  selector:
    matchLabels:
      app: perplexigate-api
  template:
    metadata:
      labels:
        app: perplexigate-api
    spec:
      containers:
      - name: api
        image: perplexigate:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: perplexigate-config
        - secretRef:
            name: perplexigate-secrets
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8000
          initialDelaySeconds: 30
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8000
          initialDelaySeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: perplexigate-api
  namespace: perplexigate
spec:
  selector:
    app: perplexigate-api
  ports:
  - port: 8000
  type: LoadBalancer
EOF

# Check deployment
kubectl get pods -n perplexigate
kubectl get services -n perplexigate
```

## 🔧 **Post-Deployment Configuration**

### **1. Monitoring Setup**
```bash
# Access monitoring dashboards
echo "API Documentation: http://localhost:8000/docs"
echo "Task Monitor (Flower): http://localhost:5555"
echo "Metrics (Prometheus): http://localhost:9090"
echo "Dashboard (Grafana): http://localhost:3000"

# Grafana default login: admin/admin
```

### **2. Backup Setup**
```bash
# Create backup script
cat > backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="./backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Database backup
docker exec postgres pg_dump -U perplexigate perplexigate > "${BACKUP_DIR}/db_backup_${DATE}.sql"
gzip "${BACKUP_DIR}/db_backup_${DATE}.sql"

# Configuration backup
cp .env "${BACKUP_DIR}/env_backup_${DATE}"

# Clean old backups (keep 7 days)
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete
find $BACKUP_DIR -name "env_backup_*" -mtime +7 -delete

echo "Backup completed: ${DATE}"
EOF

chmod +x backup.sh

# Setup cron job for daily backups
(crontab -l 2>/dev/null; echo "0 2 * * * /path/to/your/project/backup.sh") | crontab -
```

### **3. Scaling Commands**
```bash
# Scale API instances
docker-compose up --scale api=3

# Scale workers
docker-compose up --scale worker=6

# For Kubernetes
kubectl scale deployment perplexigate-api --replicas=5 -n perplexigate
kubectl scale deployment perplexigate-worker --replicas=10 -n perplexigate
```

## 🔍 **Verification and Testing**

### **1. Health Checks**
```bash
# System health
curl http://localhost:8000/health

# Component health
curl http://localhost:8000/health/ready
curl http://localhost:8000/health/live
```

### **2. Performance Testing**
```bash
# Install Apache Bench
sudo apt install apache2-utils

# Load test (adjust concurrent users and requests)
ab -n 100 -c 10 -H "Authorization: Bearer YOUR_API_KEY" \
   -p test_query.json -T application/json \
   http://localhost:8000/api/v1/query

# Create test query file
echo '{"query":"What is AI?","search_mode":"quick"}' > test_query.json
```

### **3. Monitoring**
```bash
# Check logs
docker-compose logs -f api
docker-compose logs -f worker

# Monitor resources
docker stats

# Check queue status
docker exec redis redis-cli info
```

## 🚨 **Troubleshooting**

### **Common Issues:**

1. **Services won't start:**
   ```bash
   # Check logs
   docker-compose logs
   
   # Check disk space
   df -h
   
   # Check memory
   free -h
   ```

2. **Browser crashes:**
   ```bash
   # Increase memory in docker-compose.override.yml
   # Reduce BROWSER_POOL_SIZE in .env
   ```

3. **High queue depth:**
   ```bash
   # Scale workers
   docker-compose up --scale worker=8
   
   # Check worker logs
   docker-compose logs worker
   ```

This deployment guide provides multiple options from simple local development to production Kubernetes deployment. Choose the option that best fits your infrastructure and requirements.
