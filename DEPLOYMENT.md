# PerplexiGate Deployment Guide

This guide provides detailed instructions for deploying PerplexiGate in various environments.

## Prerequisites

- Docker 20.10+ and Docker Compose 2.0+
- Minimum 4GB RAM (8GB recommended)
- 2+ CPU cores
- 20GB+ disk space
- Network access to Perplexity.ai

## Quick Start

1. **Clone and Configure**
```bash
git clone <repository-url>
cd BellingByte-PerplexiGate
cp .env.example .env
```

2. **Edit Configuration**
```bash
# Edit .env file with your settings
nano .env
```

3. **Start Services**
```bash
docker-compose up -d
```

4. **Verify Deployment**
```bash
curl http://localhost:8000/health
```

## Environment Configuration

### Required Environment Variables

```env
# Database
DATABASE_URL=************************************************/perplexigate

# Redis
REDIS_URL=redis://redis:6379/0

# Security
SECRET_KEY=your-secure-secret-key
DEFAULT_API_KEY=your-api-key

# Browser Configuration
BROWSER_POOL_SIZE=5
HEADLESS_MODE=true
```

### Optional Configuration

```env
# Performance Tuning
API_WORKERS=4
CELERY_WORKERS=4
MAX_CONCURRENT_REQUESTS=50

# Monitoring
ENABLE_METRICS=true
LOG_LEVEL=INFO

# Features
ENABLE_DEEP_SEARCH=true
ENABLE_SCREENSHOTS=true
```

## Production Deployment

### 1. Security Hardening

```bash
# Generate secure keys
openssl rand -hex 32  # For SECRET_KEY
openssl rand -hex 16  # For API keys
```

### 2. Resource Allocation

```yaml
# docker-compose.override.yml
version: '3.8'
services:
  api:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
  
  worker:
    deploy:
      replicas: 4
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
```

### 3. Load Balancing

```nginx
# nginx/nginx.conf
upstream perplexigate_api {
    server api:8000;
    # Add more API instances for scaling
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://perplexigate_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 4. SSL/TLS Configuration

```bash
# Generate SSL certificates
certbot certonly --webroot -w /var/www/html -d your-domain.com
```

## Kubernetes Deployment

### 1. Namespace and ConfigMap

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: perplexigate

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: perplexigate-config
  namespace: perplexigate
data:
  DATABASE_URL: "************************************************/perplexigate"
  REDIS_URL: "redis://redis:6379/0"
  BROWSER_POOL_SIZE: "5"
  HEADLESS_MODE: "true"
```

### 2. Secrets

```yaml
# k8s/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: perplexigate-secrets
  namespace: perplexigate
type: Opaque
data:
  SECRET_KEY: <base64-encoded-secret>
  DEFAULT_API_KEY: <base64-encoded-api-key>
```

### 3. Deployments

```yaml
# k8s/api-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: perplexigate-api
  namespace: perplexigate
spec:
  replicas: 3
  selector:
    matchLabels:
      app: perplexigate-api
  template:
    metadata:
      labels:
        app: perplexigate-api
    spec:
      containers:
      - name: api
        image: perplexigate:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: perplexigate-config
        - secretRef:
            name: perplexigate-secrets
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Monitoring Setup

### 1. Prometheus Configuration

```yaml
# monitoring/prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
    - job_name: 'perplexigate'
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_label_app]
        action: keep
        regex: perplexigate-.*
```

### 2. Grafana Dashboards

```bash
# Import pre-built dashboards
curl -X POST \
  http://grafana:3000/api/dashboards/db \
  -H 'Content-Type: application/json' \
  -d @monitoring/grafana/perplexigate-dashboard.json
```

## Scaling Guidelines

### Horizontal Scaling

```bash
# Scale API instances
docker-compose up --scale api=3

# Scale workers
docker-compose up --scale worker=6

# Kubernetes scaling
kubectl scale deployment perplexigate-api --replicas=5 -n perplexigate
kubectl scale deployment perplexigate-worker --replicas=10 -n perplexigate
```

### Vertical Scaling

```yaml
# Increase resource limits
resources:
  limits:
    memory: 8G
    cpus: '4.0'
  requests:
    memory: 4G
    cpus: '2.0'
```

### Browser Pool Scaling

```env
# Increase browser pool size
BROWSER_POOL_SIZE=10
CELERY_WORKERS=8
```

## Backup and Recovery

### Database Backup

```bash
# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="perplexigate_backup_${DATE}.sql"

docker exec postgres pg_dump -U perplexigate perplexigate > "${BACKUP_DIR}/${BACKUP_FILE}"
gzip "${BACKUP_DIR}/${BACKUP_FILE}"

# Keep only last 7 days of backups
find ${BACKUP_DIR} -name "perplexigate_backup_*.sql.gz" -mtime +7 -delete
```

### Configuration Backup

```bash
# Backup configuration and secrets
kubectl get configmap perplexigate-config -o yaml > config-backup.yaml
kubectl get secret perplexigate-secrets -o yaml > secrets-backup.yaml
```

## Troubleshooting

### Common Issues

1. **Browser crashes**
   - Increase memory limits
   - Reduce browser pool size
   - Check system resources

2. **High queue depth**
   - Scale workers
   - Optimize browser timeout settings
   - Check Perplexity response times

3. **Database connection issues**
   - Check connection pool settings
   - Verify database health
   - Review connection limits

### Debug Commands

```bash
# Check service logs
docker-compose logs -f api
docker-compose logs -f worker

# Check resource usage
docker stats

# Monitor queue
docker exec redis redis-cli monitor

# Database queries
docker exec postgres psql -U perplexigate -c "SELECT * FROM task_statistics;"
```

### Performance Tuning

```env
# Optimize for high throughput
BROWSER_POOL_SIZE=15
CELERY_WORKERS=12
MAX_CONCURRENT_REQUESTS=100
CONNECTION_POOL_SIZE=200
TASK_TIMEOUT=180
```

## Security Considerations

1. **API Security**
   - Use strong API keys
   - Implement rate limiting
   - Enable HTTPS only

2. **Network Security**
   - Use private networks
   - Restrict external access
   - Implement firewall rules

3. **Container Security**
   - Use non-root users
   - Scan images for vulnerabilities
   - Keep base images updated

4. **Data Security**
   - Encrypt sensitive data
   - Secure backup storage
   - Implement access controls
