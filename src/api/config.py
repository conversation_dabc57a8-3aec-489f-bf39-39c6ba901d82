"""
Configuration management for PerplexiGate API.
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    api_workers: int = Field(default=4, env="API_WORKERS")
    api_key_required: bool = Field(default=True, env="API_KEY_REQUIRED")
    default_api_key: str = Field(default="", env="DEFAULT_API_KEY")
    
    # Database Configuration
    database_url: str = Field(env="DATABASE_URL")
    database_pool_size: int = Field(default=20, env="DATABASE_POOL_SIZE")
    database_max_overflow: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    
    # Redis Configuration
    redis_url: str = Field(env="REDIS_URL")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    redis_db: int = Field(default=0, env="REDIS_DB")
    
    # Browser Configuration
    browser_pool_size: int = Field(default=5, env="BROWSER_POOL_SIZE")
    browser_timeout: int = Field(default=60, env="BROWSER_TIMEOUT")
    browser_navigation_timeout: int = Field(default=30, env="BROWSER_NAVIGATION_TIMEOUT")
    headless_mode: bool = Field(default=True, env="HEADLESS_MODE")
    browser_viewport_width: int = Field(default=1920, env="BROWSER_VIEWPORT_WIDTH")
    browser_viewport_height: int = Field(default=1080, env="BROWSER_VIEWPORT_HEIGHT")
    enable_screenshots: bool = Field(default=True, env="ENABLE_SCREENSHOTS")
    screenshot_on_error: bool = Field(default=True, env="SCREENSHOT_ON_ERROR")
    
    # Celery Configuration
    celery_workers: int = Field(default=4, env="CELERY_WORKERS")
    task_timeout: int = Field(default=300, env="TASK_TIMEOUT")
    task_soft_timeout: int = Field(default=240, env="TASK_SOFT_TIMEOUT")
    task_max_retries: int = Field(default=3, env="TASK_MAX_RETRIES")
    task_retry_delay: int = Field(default=60, env="TASK_RETRY_DELAY")
    
    # Perplexity Configuration
    perplexity_base_url: str = Field(default="https://www.perplexity.ai", env="PERPLEXITY_BASE_URL")
    default_search_mode: str = Field(default="quick", env="DEFAULT_SEARCH_MODE")
    default_focus_mode: str = Field(default="all", env="DEFAULT_FOCUS_MODE")
    enable_deep_search: bool = Field(default=True, env="ENABLE_DEEP_SEARCH")
    max_follow_up_questions: int = Field(default=5, env="MAX_FOLLOW_UP_QUESTIONS")
    session_timeout: int = Field(default=3600, env="SESSION_TIMEOUT")
    
    # Rate Limiting
    rate_limit_requests_per_minute: int = Field(default=60, env="RATE_LIMIT_REQUESTS_PER_MINUTE")
    rate_limit_burst: int = Field(default=10, env="RATE_LIMIT_BURST")
    rate_limit_storage: str = Field(default="redis", env="RATE_LIMIT_STORAGE")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")
    log_file_path: str = Field(default="/app/logs/perplexigate.log", env="LOG_FILE_PATH")
    log_max_size: str = Field(default="100MB", env="LOG_MAX_SIZE")
    log_backup_count: int = Field(default=5, env="LOG_BACKUP_COUNT")
    
    # Monitoring Configuration
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    metrics_port: int = Field(default=9000, env="METRICS_PORT")
    prometheus_multiproc_dir: str = Field(default="/tmp/prometheus_multiproc_dir", env="PROMETHEUS_MULTIPROC_DIR")
    
    # Security Configuration
    secret_key: str = Field(env="SECRET_KEY")
    cors_origins: List[str] = Field(default=["*"], env="CORS_ORIGINS")
    allowed_hosts: List[str] = Field(default=["*"], env="ALLOWED_HOSTS")
    
    # Performance Configuration
    max_concurrent_requests: int = Field(default=50, env="MAX_CONCURRENT_REQUESTS")
    request_timeout: int = Field(default=300, env="REQUEST_TIMEOUT")
    connection_pool_size: int = Field(default=100, env="CONNECTION_POOL_SIZE")
    keep_alive_timeout: int = Field(default=5, env="KEEP_ALIVE_TIMEOUT")
    
    # Feature Flags
    enable_caching: bool = Field(default=True, env="ENABLE_CACHING")
    cache_ttl: int = Field(default=300, env="CACHE_TTL")
    enable_request_validation: bool = Field(default=True, env="ENABLE_REQUEST_VALIDATION")
    enable_response_compression: bool = Field(default=True, env="ENABLE_RESPONSE_COMPRESSION")
    
    # Development Configuration
    debug: bool = Field(default=False, env="DEBUG")
    reload: bool = Field(default=False, env="RELOAD")
    testing: bool = Field(default=False, env="TESTING")
    
    @validator("cors_origins", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("allowed_hosts", pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level. Must be one of: {valid_levels}")
        return v.upper()
    
    @validator("default_search_mode")
    def validate_search_mode(cls, v):
        valid_modes = ["quick", "pro", "focus"]
        if v.lower() not in valid_modes:
            raise ValueError(f"Invalid search mode. Must be one of: {valid_modes}")
        return v.lower()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
