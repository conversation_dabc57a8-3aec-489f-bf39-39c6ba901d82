"""
Database models for PerplexiGate.
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any

from sqlalchemy import Column, String, DateTime, Integer, Text, Boolean, JSON, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class TaskStatus(str, Enum):
    """Task status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class SearchMode(str, Enum):
    """Perplexity search mode enumeration."""
    QUICK = "quick"
    PRO = "pro"
    FOCUS = "focus"


class FocusMode(str, Enum):
    """Perplexity focus mode enumeration."""
    ALL = "all"
    ACADEMIC = "academic"
    WRITING = "writing"
    WOLFRAM = "wolfram"
    YOUTUBE = "youtube"
    REDDIT = "reddit"
    NEWS = "news"


class QueryTask(Base):
    """Query task model."""
    __tablename__ = "query_tasks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    task_id = Column(String(255), unique=True, nullable=False, index=True)
    session_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    
    # Query details
    query = Column(Text, nullable=False)
    search_mode = Column(String(50), nullable=False, default=SearchMode.QUICK)
    focus_mode = Column(String(50), nullable=False, default=FocusMode.ALL)
    deep_search = Column(Boolean, default=False)
    sources = Column(JSON, nullable=True)
    
    # Task status
    status = Column(String(50), nullable=False, default=TaskStatus.PENDING, index=True)
    progress = Column(Integer, default=0)
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    
    # Response data
    response_data = Column(JSON, nullable=True)
    citations = Column(JSON, nullable=True)
    processing_time = Column(Float, nullable=True)
    
    # Metadata
    client_ip = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    api_key_hash = Column(String(255), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<QueryTask(id={self.id}, task_id={self.task_id}, status={self.status})>"


class Session(Base):
    """Session model for conversation context."""
    __tablename__ = "sessions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(String(255), unique=True, nullable=False, index=True)
    
    # Session details
    title = Column(String(500), nullable=True)
    context_data = Column(JSON, nullable=True)
    conversation_history = Column(JSON, nullable=True)
    
    # Configuration
    default_search_mode = Column(String(50), nullable=True)
    default_focus_mode = Column(String(50), nullable=True)
    default_deep_search = Column(Boolean, default=False)
    
    # Status
    is_active = Column(Boolean, default=True)
    last_activity = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Metadata
    client_ip = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    api_key_hash = Column(String(255), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<Session(id={self.id}, session_id={self.session_id}, active={self.is_active})>"


class BrowserInstance(Base):
    """Browser instance tracking model."""
    __tablename__ = "browser_instances"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    instance_id = Column(String(255), unique=True, nullable=False, index=True)
    
    # Instance details
    browser_type = Column(String(50), nullable=False)  # chromium, firefox, webkit
    worker_id = Column(String(255), nullable=True)
    process_id = Column(Integer, nullable=True)
    
    # Status
    status = Column(String(50), nullable=False, default="idle")  # idle, busy, crashed, terminated
    current_task_id = Column(String(255), nullable=True)
    last_activity = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Performance metrics
    total_tasks = Column(Integer, default=0)
    successful_tasks = Column(Integer, default=0)
    failed_tasks = Column(Integer, default=0)
    average_response_time = Column(Float, nullable=True)
    
    # Resource usage
    memory_usage = Column(Float, nullable=True)
    cpu_usage = Column(Float, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    terminated_at = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<BrowserInstance(id={self.id}, instance_id={self.instance_id}, status={self.status})>"


class ApiKey(Base):
    """API key model."""
    __tablename__ = "api_keys"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    key_hash = Column(String(255), unique=True, nullable=False, index=True)
    
    # Key details
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Permissions and limits
    is_active = Column(Boolean, default=True)
    rate_limit = Column(Integer, nullable=True)  # requests per minute
    daily_limit = Column(Integer, nullable=True)  # requests per day
    monthly_limit = Column(Integer, nullable=True)  # requests per month
    
    # Usage tracking
    total_requests = Column(Integer, default=0)
    last_used = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<ApiKey(id={self.id}, name={self.name}, active={self.is_active})>"
