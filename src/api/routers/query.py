"""
Query router for PerplexiGate API.
"""

import uuid
from datetime import datetime
from typing import Optional, List

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Request
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from ..database import get_db_session
from ..models import QueryTask, Session, TaskStatus
from ..schemas import (
    QueryRequest, TaskResponse, SessionResponse, SessionCreateRequest,
    TaskMetadata, PaginationParams, PaginatedResponse
)
from ..services.task_service import TaskService
from ..services.session_service import SessionService
from ..exceptions import PerplexiGateException

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.post("/query", response_model=TaskResponse)
async def submit_query(
    request: QueryRequest,
    background_tasks: BackgroundTasks,
    http_request: Request,
    db: AsyncSession = Depends(get_db_session)
):
    """
    Submit a query for processing.
    
    This endpoint accepts a query and submits it to the processing queue.
    Returns a task ID that can be used to check the status and retrieve results.
    """
    try:
        logger.info("Received query request", query=request.query[:100], search_mode=request.search_mode)
        
        # Create task service
        task_service = TaskService(db)
        
        # Generate task ID
        task_id = str(uuid.uuid4())
        
        # Extract client information
        client_ip = http_request.client.host
        user_agent = http_request.headers.get("user-agent", "")
        
        # Create task record
        task = await task_service.create_task(
            task_id=task_id,
            query=request.query,
            search_mode=request.search_mode,
            focus_mode=request.focus_mode,
            deep_search=request.deep_search,
            sources=request.sources,
            session_id=request.session_id,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        # Submit to queue
        background_tasks.add_task(
            task_service.submit_to_queue,
            task_id,
            request.dict()
        )
        
        # Return task response
        return TaskResponse(
            task_id=task_id,
            status=TaskStatus.PENDING,
            progress=0,
            response=None,
            error=None,
            metadata=TaskMetadata(
                processing_time=None,
                session_id=request.session_id,
                timestamp=datetime.utcnow(),
                retry_count=0
            )
        )
        
    except Exception as e:
        logger.error("Failed to submit query", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to submit query")


@router.get("/query/{task_id}", response_model=TaskResponse)
async def get_task_status(
    task_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """
    Get task status and results.
    
    Returns the current status of a task and the results if completed.
    """
    try:
        task_service = TaskService(db)
        task = await task_service.get_task(task_id)
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return await task_service.format_task_response(task)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get task status", task_id=task_id, error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to get task status")


@router.delete("/query/{task_id}")
async def cancel_task(
    task_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """
    Cancel a pending or processing task.
    """
    try:
        task_service = TaskService(db)
        success = await task_service.cancel_task(task_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Task not found or cannot be cancelled")
        
        return {"message": "Task cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to cancel task", task_id=task_id, error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to cancel task")


@router.get("/tasks", response_model=PaginatedResponse)
async def list_tasks(
    pagination: PaginationParams = Depends(),
    status: Optional[TaskStatus] = None,
    session_id: Optional[str] = None,
    db: AsyncSession = Depends(get_db_session)
):
    """
    List tasks with optional filtering.
    """
    try:
        task_service = TaskService(db)
        tasks, total = await task_service.list_tasks(
            page=pagination.page,
            size=pagination.size,
            status=status,
            session_id=session_id
        )
        
        # Calculate pagination info
        pages = (total + pagination.size - 1) // pagination.size
        has_next = pagination.page < pages
        has_prev = pagination.page > 1
        
        return PaginatedResponse(
            items=[await task_service.format_task_response(task) for task in tasks],
            total=total,
            page=pagination.page,
            size=pagination.size,
            pages=pages,
            has_next=has_next,
            has_prev=has_prev
        )
        
    except Exception as e:
        logger.error("Failed to list tasks", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to list tasks")


@router.post("/sessions", response_model=SessionResponse)
async def create_session(
    request: SessionCreateRequest,
    http_request: Request,
    db: AsyncSession = Depends(get_db_session)
):
    """
    Create a new session for conversation context.
    """
    try:
        session_service = SessionService(db)
        
        # Extract client information
        client_ip = http_request.client.host
        user_agent = http_request.headers.get("user-agent", "")
        
        session = await session_service.create_session(
            title=request.title,
            default_search_mode=request.default_search_mode,
            default_focus_mode=request.default_focus_mode,
            default_deep_search=request.default_deep_search,
            client_ip=client_ip,
            user_agent=user_agent
        )
        
        return await session_service.format_session_response(session)
        
    except Exception as e:
        logger.error("Failed to create session", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to create session")


@router.get("/sessions/{session_id}", response_model=SessionResponse)
async def get_session(
    session_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """
    Get session information.
    """
    try:
        session_service = SessionService(db)
        session = await session_service.get_session(session_id)
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return await session_service.format_session_response(session)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get session", session_id=session_id, error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to get session")


@router.delete("/sessions/{session_id}")
async def delete_session(
    session_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """
    Delete a session and its conversation history.
    """
    try:
        session_service = SessionService(db)
        success = await session_service.delete_session(session_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return {"message": "Session deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete session", session_id=session_id, error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to delete session")


@router.get("/sessions", response_model=PaginatedResponse)
async def list_sessions(
    pagination: PaginationParams = Depends(),
    active_only: bool = True,
    db: AsyncSession = Depends(get_db_session)
):
    """
    List sessions with optional filtering.
    """
    try:
        session_service = SessionService(db)
        sessions, total = await session_service.list_sessions(
            page=pagination.page,
            size=pagination.size,
            active_only=active_only
        )
        
        # Calculate pagination info
        pages = (total + pagination.size - 1) // pagination.size
        has_next = pagination.page < pages
        has_prev = pagination.page > 1
        
        return PaginatedResponse(
            items=[await session_service.format_session_response(session) for session in sessions],
            total=total,
            page=pagination.page,
            size=pagination.size,
            pages=pages,
            has_next=has_next,
            has_prev=has_prev
        )
        
    except Exception as e:
        logger.error("Failed to list sessions", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to list sessions")
