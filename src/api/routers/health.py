"""
Health check router for PerplexiGate API.
"""

import time
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
import redis
import structlog

from ..database import get_db_session, check_db_health
from ..schemas import HealthResponse
from ..config import get_settings

logger = structlog.get_logger(__name__)
router = APIRouter()

# Track service start time
SERVICE_START_TIME = time.time()


async def check_redis_health() -> bool:
    """Check Redis connectivity."""
    try:
        settings = get_settings()
        redis_client = redis.from_url(settings.redis_url)
        redis_client.ping()
        redis_client.close()
        return True
    except Exception as e:
        logger.error("Redis health check failed", error=str(e))
        return False


async def check_celery_health() -> bool:
    """Check Celery worker availability."""
    try:
        from ...worker.tasks import submit_health_check
        
        # Submit a health check task with short timeout
        task = submit_health_check()
        result = task.get(timeout=5)
        
        return result.get("status") == "healthy"
        
    except Exception as e:
        logger.error("Celery health check failed", error=str(e))
        return False


@router.get("/", response_model=HealthResponse)
async def health_check(db: AsyncSession = Depends(get_db_session)):
    """
    Comprehensive health check endpoint.
    
    Checks the health of all system components:
    - API service
    - Database connectivity
    - Redis connectivity
    - Celery workers
    """
    try:
        logger.debug("Performing health check")
        
        # Check database
        db_healthy = await check_db_health()
        
        # Check Redis
        redis_healthy = await check_redis_health()
        
        # Check Celery workers
        celery_healthy = await check_celery_health()
        
        # Determine overall status
        components = {
            "database": "healthy" if db_healthy else "unhealthy",
            "redis": "healthy" if redis_healthy else "unhealthy",
            "celery": "healthy" if celery_healthy else "unhealthy",
            "api": "healthy"  # If we're responding, API is healthy
        }
        
        overall_status = "healthy" if all(
            status == "healthy" for status in components.values()
        ) else "degraded"
        
        # Calculate uptime
        uptime = time.time() - SERVICE_START_TIME
        
        response = HealthResponse(
            status=overall_status,
            version="1.0.0",
            timestamp=datetime.utcnow(),
            components=components,
            uptime=uptime
        )
        
        logger.debug("Health check completed", status=overall_status, components=components)
        return response
        
    except Exception as e:
        logger.error("Health check failed", error=str(e), exc_info=True)
        
        return HealthResponse(
            status="unhealthy",
            version="1.0.0",
            timestamp=datetime.utcnow(),
            components={"api": "unhealthy"},
            uptime=time.time() - SERVICE_START_TIME
        )


@router.get("/ready")
async def readiness_check():
    """
    Kubernetes readiness probe endpoint.
    
    Returns 200 if the service is ready to accept traffic.
    """
    try:
        # Quick check of essential services
        db_healthy = await check_db_health()
        redis_healthy = await check_redis_health()
        
        if db_healthy and redis_healthy:
            return {"status": "ready"}
        else:
            return {"status": "not ready"}, 503
            
    except Exception:
        return {"status": "not ready"}, 503


@router.get("/live")
async def liveness_check():
    """
    Kubernetes liveness probe endpoint.
    
    Returns 200 if the service is alive (basic functionality).
    """
    return {"status": "alive", "timestamp": datetime.utcnow()}


@router.get("/metrics")
async def basic_metrics(db: AsyncSession = Depends(get_db_session)):
    """
    Basic metrics endpoint for monitoring.
    """
    try:
        from ..models import QueryTask, TaskStatus
        from sqlalchemy import select, func
        
        # Get task statistics
        total_tasks_result = await db.execute(
            select(func.count()).select_from(QueryTask)
        )
        total_tasks = total_tasks_result.scalar() or 0
        
        # Get tasks by status
        status_counts = {}
        for status in TaskStatus:
            count_result = await db.execute(
                select(func.count()).select_from(QueryTask)
                .where(QueryTask.status == status.value)
            )
            status_counts[status.value] = count_result.scalar() or 0
        
        # Get recent task statistics (last hour)
        from datetime import timedelta
        one_hour_ago = datetime.utcnow() - timedelta(hours=1)
        
        recent_tasks_result = await db.execute(
            select(func.count()).select_from(QueryTask)
            .where(QueryTask.created_at >= one_hour_ago)
        )
        recent_tasks = recent_tasks_result.scalar() or 0
        
        # Calculate average processing time
        avg_time_result = await db.execute(
            select(func.avg(QueryTask.processing_time))
            .where(QueryTask.processing_time.isnot(None))
            .where(QueryTask.created_at >= one_hour_ago)
        )
        avg_processing_time = avg_time_result.scalar() or 0
        
        return {
            "total_tasks": total_tasks,
            "status_counts": status_counts,
            "recent_tasks_1h": recent_tasks,
            "avg_processing_time": float(avg_processing_time),
            "uptime": time.time() - SERVICE_START_TIME,
            "timestamp": datetime.utcnow()
        }
        
    except Exception as e:
        logger.error("Failed to get metrics", error=str(e), exc_info=True)
        return {"error": "Failed to get metrics"}, 500
