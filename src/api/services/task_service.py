"""
Task service for managing query processing tasks.
"""

import uuid
from datetime import datetime
from typing import List, Op<PERSON>, Tu<PERSON>, Dict, Any

from sqlalchemy import select, update, delete, func
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from ..models import QueryTask, TaskStatus, SearchMode, FocusMode
from ..schemas import TaskResponse, TaskMetadata, QueryResponse, Citation
from ..config import get_settings
from ...worker.tasks import submit_query_task

logger = structlog.get_logger(__name__)


class TaskService:
    """Service for managing query processing tasks."""
    
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
        self.settings = get_settings()
    
    async def create_task(
        self,
        task_id: str,
        query: str,
        search_mode: SearchMode,
        focus_mode: FocusMode,
        deep_search: bool,
        sources: Optional[List[str]] = None,
        session_id: Optional[str] = None,
        client_ip: Optional[str] = None,
        user_agent: Optional[str] = None,
        api_key_hash: Optional[str] = None
    ) -> QueryTask:
        """Create a new query task."""
        try:
            task = QueryTask(
                task_id=task_id,
                session_id=session_id,
                query=query,
                search_mode=search_mode.value,
                focus_mode=focus_mode.value,
                deep_search=deep_search,
                sources=sources,
                status=TaskStatus.PENDING,
                client_ip=client_ip,
                user_agent=user_agent,
                api_key_hash=api_key_hash
            )
            
            self.db.add(task)
            await self.db.commit()
            await self.db.refresh(task)
            
            logger.info("Task created", task_id=task_id, query=query[:100])
            return task
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to create task", task_id=task_id, error=str(e), exc_info=True)
            raise
    
    async def get_task(self, task_id: str) -> Optional[QueryTask]:
        """Get task by ID."""
        try:
            result = await self.db.execute(
                select(QueryTask).where(QueryTask.task_id == task_id)
            )
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error("Failed to get task", task_id=task_id, error=str(e), exc_info=True)
            return None
    
    async def update_task_status(
        self,
        task_id: str,
        status: TaskStatus,
        progress: Optional[int] = None,
        error_message: Optional[str] = None,
        response_data: Optional[Dict[str, Any]] = None,
        processing_time: Optional[float] = None
    ) -> bool:
        """Update task status and related fields."""
        try:
            update_data = {
                "status": status.value,
                "updated_at": datetime.utcnow()
            }
            
            if progress is not None:
                update_data["progress"] = progress
            
            if error_message:
                update_data["error_message"] = error_message
            
            if response_data:
                update_data["response_data"] = response_data
            
            if processing_time:
                update_data["processing_time"] = processing_time
            
            if status == TaskStatus.PROCESSING:
                update_data["started_at"] = datetime.utcnow()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                update_data["completed_at"] = datetime.utcnow()
            
            result = await self.db.execute(
                update(QueryTask)
                .where(QueryTask.task_id == task_id)
                .values(**update_data)
            )
            
            await self.db.commit()
            
            if result.rowcount > 0:
                logger.info("Task status updated", task_id=task_id, status=status.value)
                return True
            else:
                logger.warning("Task not found for status update", task_id=task_id)
                return False
                
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to update task status", task_id=task_id, error=str(e), exc_info=True)
            return False
    
    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a pending or processing task."""
        try:
            # Get current task
            task = await self.get_task(task_id)
            if not task:
                return False
            
            # Check if task can be cancelled
            if task.status not in [TaskStatus.PENDING, TaskStatus.PROCESSING]:
                return False
            
            # Update status to cancelled
            success = await self.update_task_status(task_id, TaskStatus.CANCELLED)
            
            if success:
                # TODO: Cancel the actual Celery task if it's running
                logger.info("Task cancelled", task_id=task_id)
            
            return success
            
        except Exception as e:
            logger.error("Failed to cancel task", task_id=task_id, error=str(e), exc_info=True)
            return False
    
    async def list_tasks(
        self,
        page: int = 1,
        size: int = 20,
        status: Optional[TaskStatus] = None,
        session_id: Optional[str] = None
    ) -> Tuple[List[QueryTask], int]:
        """List tasks with pagination and filtering."""
        try:
            # Build query
            query = select(QueryTask)
            
            if status:
                query = query.where(QueryTask.status == status.value)
            
            if session_id:
                query = query.where(QueryTask.session_id == session_id)
            
            # Get total count
            count_query = select(func.count()).select_from(query.subquery())
            total_result = await self.db.execute(count_query)
            total = total_result.scalar()
            
            # Apply pagination
            offset = (page - 1) * size
            query = query.order_by(QueryTask.created_at.desc()).offset(offset).limit(size)
            
            # Execute query
            result = await self.db.execute(query)
            tasks = result.scalars().all()
            
            return list(tasks), total
            
        except Exception as e:
            logger.error("Failed to list tasks", error=str(e), exc_info=True)
            return [], 0
    
    async def submit_to_queue(self, task_id: str, query_data: Dict[str, Any]):
        """Submit task to processing queue."""
        try:
            # Submit to Celery
            celery_task = submit_query_task(task_id, query_data)
            
            logger.info("Task submitted to queue", 
                       task_id=task_id, 
                       celery_task_id=celery_task.id)
            
        except Exception as e:
            logger.error("Failed to submit task to queue", task_id=task_id, error=str(e), exc_info=True)
            # Update task status to failed
            await self.update_task_status(
                task_id, 
                TaskStatus.FAILED, 
                error_message=f"Failed to submit to queue: {str(e)}"
            )
    
    async def format_task_response(self, task: QueryTask) -> TaskResponse:
        """Format task data as API response."""
        try:
            # Parse response data if available
            query_response = None
            if task.response_data:
                citations = []
                if task.response_data.get("citations"):
                    citations = [
                        Citation(**citation_data) 
                        for citation_data in task.response_data["citations"]
                    ]
                
                query_response = QueryResponse(
                    answer=task.response_data.get("answer", ""),
                    citations=citations,
                    sources=task.response_data.get("sources", []),
                    search_mode=SearchMode(task.response_data.get("search_mode", "quick")),
                    focus_mode=FocusMode(task.response_data.get("focus_mode", "all")),
                    deep_search_used=task.response_data.get("deep_search_used", False),
                    follow_up_suggestions=task.response_data.get("follow_up_suggestions")
                )
            
            # Create metadata
            metadata = TaskMetadata(
                processing_time=task.processing_time,
                session_id=str(task.session_id) if task.session_id else None,
                timestamp=task.completed_at or task.updated_at,
                retry_count=task.retry_count
            )
            
            return TaskResponse(
                task_id=task.task_id,
                status=TaskStatus(task.status),
                progress=task.progress,
                response=query_response,
                error=task.error_message,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error("Failed to format task response", task_id=task.task_id, error=str(e), exc_info=True)
            raise
    
    async def cleanup_old_tasks(self, days: int = 7) -> int:
        """Clean up old completed tasks."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            result = await self.db.execute(
                delete(QueryTask)
                .where(
                    QueryTask.status.in_([
                        TaskStatus.COMPLETED.value,
                        TaskStatus.FAILED.value,
                        TaskStatus.CANCELLED.value
                    ])
                )
                .where(QueryTask.completed_at < cutoff_date)
            )
            
            await self.db.commit()
            
            deleted_count = result.rowcount
            logger.info("Old tasks cleaned up", deleted_count=deleted_count, days=days)
            
            return deleted_count
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to cleanup old tasks", error=str(e), exc_info=True)
            return 0
