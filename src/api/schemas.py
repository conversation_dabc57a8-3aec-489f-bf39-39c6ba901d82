"""
Pydantic schemas for PerplexiGate API.
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from enum import Enum

from pydantic import BaseModel, Field, validator


class SearchMode(str, Enum):
    """Search mode enumeration."""
    QUICK = "quick"
    PRO = "pro"
    FOCUS = "focus"


class FocusMode(str, Enum):
    """Focus mode enumeration."""
    ALL = "all"
    ACADEMIC = "academic"
    WRITING = "writing"
    WOLFRAM = "wolfram"
    YOUTUBE = "youtube"
    REDDIT = "reddit"
    NEWS = "news"


class TaskStatus(str, Enum):
    """Task status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class SourceType(str, Enum):
    """Source type enumeration."""
    ACADEMIC = "academic"
    NEWS = "news"
    REDDIT = "reddit"
    YOUTUBE = "youtube"
    WRITING = "writing"
    WOLFRAM = "wolfram"
    ALL = "all"


# Request Schemas
class QueryRequest(BaseModel):
    """Query request schema."""
    query: str = Field(..., min_length=1, max_length=2000, description="Search query")
    search_mode: SearchMode = Field(default=SearchMode.QUICK, description="Search mode")
    focus_mode: FocusMode = Field(default=FocusMode.ALL, description="Focus mode")
    deep_search: bool = Field(default=False, description="Enable deep search")
    sources: Optional[List[SourceType]] = Field(default=None, description="Source filters")
    session_id: Optional[str] = Field(default=None, description="Session ID for context")
    follow_up: bool = Field(default=False, description="Is this a follow-up question")
    
    @validator("query")
    def validate_query(cls, v):
        if not v.strip():
            raise ValueError("Query cannot be empty")
        return v.strip()
    
    @validator("sources")
    def validate_sources(cls, v):
        if v is not None and len(v) == 0:
            return None
        return v


class SessionCreateRequest(BaseModel):
    """Session creation request schema."""
    title: Optional[str] = Field(default=None, max_length=500, description="Session title")
    default_search_mode: Optional[SearchMode] = Field(default=None, description="Default search mode")
    default_focus_mode: Optional[FocusMode] = Field(default=None, description="Default focus mode")
    default_deep_search: Optional[bool] = Field(default=None, description="Default deep search setting")


# Response Schemas
class Citation(BaseModel):
    """Citation schema."""
    title: str = Field(..., description="Source title")
    url: str = Field(..., description="Source URL")
    snippet: Optional[str] = Field(default=None, description="Relevant excerpt")
    domain: Optional[str] = Field(default=None, description="Source domain")
    published_date: Optional[datetime] = Field(default=None, description="Publication date")
    source_type: Optional[str] = Field(default=None, description="Type of source")


class QueryResponse(BaseModel):
    """Query response schema."""
    answer: str = Field(..., description="Formatted response text")
    citations: List[Citation] = Field(default=[], description="Source citations")
    sources: List[str] = Field(default=[], description="Source types used")
    search_mode: SearchMode = Field(..., description="Search mode used")
    focus_mode: FocusMode = Field(..., description="Focus mode used")
    deep_search_used: bool = Field(default=False, description="Whether deep search was used")
    follow_up_suggestions: Optional[List[str]] = Field(default=None, description="Suggested follow-up questions")


class TaskMetadata(BaseModel):
    """Task metadata schema."""
    processing_time: Optional[float] = Field(default=None, description="Processing time in seconds")
    session_id: Optional[str] = Field(default=None, description="Session ID")
    timestamp: datetime = Field(..., description="Task completion timestamp")
    retry_count: int = Field(default=0, description="Number of retries")
    worker_id: Optional[str] = Field(default=None, description="Worker that processed the task")


class TaskResponse(BaseModel):
    """Task response schema."""
    task_id: str = Field(..., description="Unique task identifier")
    status: TaskStatus = Field(..., description="Task status")
    progress: int = Field(default=0, ge=0, le=100, description="Progress percentage")
    response: Optional[QueryResponse] = Field(default=None, description="Query response data")
    error: Optional[str] = Field(default=None, description="Error message if failed")
    metadata: TaskMetadata = Field(..., description="Task metadata")


class SessionResponse(BaseModel):
    """Session response schema."""
    session_id: str = Field(..., description="Session identifier")
    title: Optional[str] = Field(default=None, description="Session title")
    is_active: bool = Field(..., description="Whether session is active")
    created_at: datetime = Field(..., description="Session creation time")
    last_activity: datetime = Field(..., description="Last activity time")
    expires_at: Optional[datetime] = Field(default=None, description="Session expiration time")
    conversation_count: int = Field(default=0, description="Number of conversations")


class HealthResponse(BaseModel):
    """Health check response schema."""
    status: str = Field(..., description="Service status")
    version: str = Field(..., description="Service version")
    timestamp: datetime = Field(..., description="Health check timestamp")
    components: Dict[str, str] = Field(..., description="Component health status")
    uptime: float = Field(..., description="Service uptime in seconds")


class MetricsResponse(BaseModel):
    """Metrics response schema."""
    total_requests: int = Field(..., description="Total requests processed")
    active_tasks: int = Field(..., description="Currently active tasks")
    queue_size: int = Field(..., description="Current queue size")
    average_response_time: float = Field(..., description="Average response time")
    success_rate: float = Field(..., description="Success rate percentage")
    active_sessions: int = Field(..., description="Active sessions count")
    browser_instances: int = Field(..., description="Active browser instances")


class ErrorResponse(BaseModel):
    """Error response schema."""
    error: str = Field(..., description="Error message")
    code: Optional[str] = Field(default=None, description="Error code")
    details: Optional[Dict[str, Any]] = Field(default=None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")


# Pagination Schemas
class PaginationParams(BaseModel):
    """Pagination parameters."""
    page: int = Field(default=1, ge=1, description="Page number")
    size: int = Field(default=20, ge=1, le=100, description="Page size")


class PaginatedResponse(BaseModel):
    """Paginated response schema."""
    items: List[Any] = Field(..., description="Response items")
    total: int = Field(..., description="Total items count")
    page: int = Field(..., description="Current page")
    size: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total pages")
    has_next: bool = Field(..., description="Has next page")
    has_prev: bool = Field(..., description="Has previous page")


# Configuration Schemas
class BrowserConfig(BaseModel):
    """Browser configuration schema."""
    headless: bool = Field(default=True, description="Run in headless mode")
    viewport_width: int = Field(default=1920, description="Viewport width")
    viewport_height: int = Field(default=1080, description="Viewport height")
    timeout: int = Field(default=60, description="Browser timeout in seconds")
    enable_screenshots: bool = Field(default=True, description="Enable screenshots")


class PerplexityConfig(BaseModel):
    """Perplexity configuration schema."""
    base_url: str = Field(default="https://www.perplexity.ai", description="Perplexity base URL")
    default_search_mode: SearchMode = Field(default=SearchMode.QUICK, description="Default search mode")
    default_focus_mode: FocusMode = Field(default=FocusMode.ALL, description="Default focus mode")
    enable_deep_search: bool = Field(default=True, description="Enable deep search by default")
    max_follow_up_questions: int = Field(default=5, description="Maximum follow-up questions")
    session_timeout: int = Field(default=3600, description="Session timeout in seconds")
