"""
Celery tasks for PerplexiGate worker processes.
"""

import asyncio
from typing import Dict, Any

from celery import Task
from celery.exceptions import Retry
import structlog

from .celery_app import celery_app, process_query_async

logger = structlog.get_logger(__name__)


class AsyncTask(Task):
    """Base class for async Celery tasks."""
    
    def __call__(self, *args, **kwargs):
        """Execute the task in an async context."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self.run_async(*args, **kwargs))
        finally:
            loop.close()
    
    async def run_async(self, *args, **kwargs):
        """Override this method in subclasses."""
        raise NotImplementedError


@celery_app.task(
    bind=True,
    base=AsyncTask,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    name="src.worker.tasks.process_query"
)
class ProcessQueryTask(AsyncTask):
    """Task for processing Perplexity queries."""
    
    async def run_async(self, task_id: str, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a query using browser automation.
        
        Args:
            task_id: Unique task identifier
            query_data: Query parameters and configuration
            
        Returns:
            Dict containing processing results
        """
        try:
            logger.info("Processing query task", task_id=task_id, query=query_data.get("query", "")[:100])
            
            # Process the query
            result = await process_query_async(task_id, query_data)
            
            logger.info("Query task completed", task_id=task_id, status=result.get("status"))
            return result
            
        except Exception as e:
            logger.error("Query task failed", task_id=task_id, error=str(e), exc_info=True)
            
            # Check if we should retry
            if self.request.retries < self.max_retries:
                logger.info("Retrying query task", 
                           task_id=task_id, 
                           retry_count=self.request.retries + 1)
                raise self.retry(countdown=60 * (self.request.retries + 1))
            
            # Final failure
            return {
                "status": "error",
                "error": str(e),
                "task_id": task_id
            }


@celery_app.task(
    bind=True,
    base=AsyncTask,
    name="src.worker.tasks.cleanup_browser"
)
class CleanupBrowserTask(AsyncTask):
    """Task for browser cleanup and maintenance."""
    
    async def run_async(self, instance_id: str = None) -> Dict[str, Any]:
        """
        Clean up browser instances.
        
        Args:
            instance_id: Specific instance to clean up (optional)
            
        Returns:
            Dict containing cleanup results
        """
        try:
            logger.info("Starting browser cleanup", instance_id=instance_id)
            
            from .celery_app import get_browser_pool
            
            browser_pool = get_browser_pool()
            
            if instance_id:
                # Clean up specific instance
                # This would need implementation in browser pool
                pass
            else:
                # General maintenance
                stats = browser_pool.get_stats()
                logger.info("Browser pool stats", **stats)
            
            return {"status": "success", "cleaned_instances": 1 if instance_id else 0}
            
        except Exception as e:
            logger.error("Browser cleanup failed", error=str(e), exc_info=True)
            return {"status": "error", "error": str(e)}


@celery_app.task(
    bind=True,
    base=AsyncTask,
    name="src.worker.tasks.health_check"
)
class HealthCheckTask(AsyncTask):
    """Task for worker health checks."""
    
    async def run_async(self) -> Dict[str, Any]:
        """
        Perform worker health check.
        
        Returns:
            Dict containing health status
        """
        try:
            from .celery_app import get_browser_pool
            
            # Check browser pool
            browser_pool = get_browser_pool()
            pool_stats = browser_pool.get_stats()
            
            # Check if we have healthy instances
            healthy_instances = pool_stats["idle_instances"] + pool_stats["busy_instances"]
            
            status = "healthy" if healthy_instances > 0 else "unhealthy"
            
            return {
                "status": status,
                "browser_pool": pool_stats,
                "timestamp": asyncio.get_event_loop().time()
            }
            
        except Exception as e:
            logger.error("Health check failed", error=str(e), exc_info=True)
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": asyncio.get_event_loop().time()
            }


# Convenience functions for task submission
def submit_query_task(task_id: str, query_data: Dict[str, Any]) -> Any:
    """Submit a query processing task."""
    return ProcessQueryTask.delay(task_id, query_data)


def submit_cleanup_task(instance_id: str = None) -> Any:
    """Submit a browser cleanup task."""
    return CleanupBrowserTask.delay(instance_id)


def submit_health_check() -> Any:
    """Submit a health check task."""
    return HealthCheckTask.delay()


# Periodic tasks (if using celery beat)
@celery_app.task(name="src.worker.tasks.periodic_cleanup")
def periodic_cleanup():
    """Periodic cleanup task."""
    return submit_cleanup_task()


@celery_app.task(name="src.worker.tasks.periodic_health_check")
def periodic_health_check():
    """Periodic health check task."""
    return submit_health_check()
