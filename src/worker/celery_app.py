"""
Celery application for PerplexiGate worker processes.
"""

import os
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional

from celery import Celery
from celery.signals import worker_init, worker_shutdown
import structlog

from ..api.config import get_settings
from ..api.models import QueryTask, TaskStatus
from ..api.database import get_async_session
from ..automation.perplexity_automation import PerplexityAutomation
from ..automation.browser_pool import BrowserPool

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Get settings
settings = get_settings()

# Create Celery app
celery_app = Celery(
    "perplexigate_worker",
    broker=settings.redis_url,
    backend=settings.redis_url,
    include=["src.worker.tasks"]
)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=settings.task_timeout,
    task_soft_time_limit=settings.task_soft_timeout,
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    task_routes={
        "src.worker.tasks.process_query": {"queue": "query_processing"},
        "src.worker.tasks.cleanup_browser": {"queue": "maintenance"},
    },
    task_default_queue="query_processing",
    task_default_exchange="perplexigate",
    task_default_exchange_type="direct",
    task_default_routing_key="query_processing",
    worker_hijack_root_logger=False,
    worker_log_format="[%(asctime)s: %(levelname)s/%(processName)s] %(message)s",
    worker_task_log_format="[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s",
    result_expires=3600,  # Results expire after 1 hour
    task_compression="gzip",
    result_compression="gzip",
)

# Global browser pool instance
browser_pool: Optional[BrowserPool] = None


@worker_init.connect
def worker_init_handler(sender=None, **kwargs):
    """Initialize worker resources."""
    global browser_pool
    
    logger.info("Initializing Celery worker")
    
    try:
        # Initialize browser pool
        browser_pool = BrowserPool(
            pool_size=settings.browser_pool_size,
            browser_timeout=settings.browser_timeout,
            headless=settings.headless_mode
        )
        
        # Start browser pool in async context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(browser_pool.initialize())
        
        logger.info("Worker initialized successfully")
        
    except Exception as e:
        logger.error("Failed to initialize worker", error=str(e), exc_info=True)
        raise


@worker_shutdown.connect
def worker_shutdown_handler(sender=None, **kwargs):
    """Clean up worker resources."""
    global browser_pool
    
    logger.info("Shutting down Celery worker")
    
    try:
        if browser_pool:
            # Clean up browser pool
            loop = asyncio.get_event_loop()
            loop.run_until_complete(browser_pool.cleanup())
        
        logger.info("Worker shutdown completed")
        
    except Exception as e:
        logger.error("Error during worker shutdown", error=str(e))


def get_browser_pool() -> BrowserPool:
    """Get the global browser pool instance."""
    global browser_pool
    if not browser_pool:
        raise RuntimeError("Browser pool not initialized")
    return browser_pool


async def update_task_status(
    task_id: str,
    status: TaskStatus,
    progress: int = None,
    error_message: str = None,
    response_data: Dict[str, Any] = None,
    processing_time: float = None
):
    """Update task status in database."""
    try:
        async with get_async_session() as session:
            # Get task
            result = await session.execute(
                "SELECT * FROM query_tasks WHERE task_id = :task_id",
                {"task_id": task_id}
            )
            task_row = result.fetchone()
            
            if not task_row:
                logger.error("Task not found for status update", task_id=task_id)
                return
            
            # Update task
            update_data = {
                "status": status.value,
                "updated_at": datetime.utcnow()
            }
            
            if progress is not None:
                update_data["progress"] = progress
            
            if error_message:
                update_data["error_message"] = error_message
            
            if response_data:
                update_data["response_data"] = response_data
            
            if processing_time:
                update_data["processing_time"] = processing_time
            
            if status == TaskStatus.PROCESSING:
                update_data["started_at"] = datetime.utcnow()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                update_data["completed_at"] = datetime.utcnow()
            
            # Build update query
            set_clause = ", ".join([f"{key} = :{key}" for key in update_data.keys()])
            query = f"UPDATE query_tasks SET {set_clause} WHERE task_id = :task_id"
            update_data["task_id"] = task_id
            
            await session.execute(query, update_data)
            await session.commit()
            
            logger.info("Task status updated", task_id=task_id, status=status.value)
            
    except Exception as e:
        logger.error("Failed to update task status", task_id=task_id, error=str(e), exc_info=True)


async def process_query_async(task_id: str, query_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a query asynchronously using browser automation.
    
    This is the main processing function that handles the entire query workflow.
    """
    start_time = datetime.utcnow()
    processing_start = start_time.timestamp()
    
    try:
        logger.info("Starting query processing", task_id=task_id, query=query_data.get("query", "")[:100])
        
        # Update task status to processing
        await update_task_status(task_id, TaskStatus.PROCESSING, progress=10)
        
        # Get browser instance from pool
        browser_pool = get_browser_pool()
        browser_instance = await browser_pool.acquire()
        
        try:
            # Update progress
            await update_task_status(task_id, TaskStatus.PROCESSING, progress=20)
            
            # Create automation instance
            automation = PerplexityAutomation()
            automation.browser = browser_instance.browser
            automation.context = browser_instance.context
            automation.page = browser_instance.page
            
            # Update progress
            await update_task_status(task_id, TaskStatus.PROCESSING, progress=30)
            
            # Process the query
            response = await automation.process_query(
                query=query_data["query"],
                search_mode=query_data.get("search_mode", "quick"),
                focus_mode=query_data.get("focus_mode", "all"),
                deep_search=query_data.get("deep_search", False),
                session_id=query_data.get("session_id")
            )
            
            # Update progress
            await update_task_status(task_id, TaskStatus.PROCESSING, progress=90)
            
            if not response:
                raise Exception("No response received from automation")
            
            # Calculate processing time
            processing_time = datetime.utcnow().timestamp() - processing_start
            
            # Prepare response data
            response_data = {
                "answer": response.answer,
                "citations": [citation.dict() for citation in response.citations],
                "sources": response.sources,
                "search_mode": response.search_mode.value,
                "focus_mode": response.focus_mode.value,
                "deep_search_used": response.deep_search_used,
                "follow_up_suggestions": response.follow_up_suggestions
            }
            
            # Update task as completed
            await update_task_status(
                task_id,
                TaskStatus.COMPLETED,
                progress=100,
                response_data=response_data,
                processing_time=processing_time
            )
            
            logger.info("Query processed successfully", 
                       task_id=task_id, 
                       processing_time=processing_time,
                       citations_count=len(response.citations))
            
            return {
                "status": "success",
                "response": response_data,
                "processing_time": processing_time
            }
            
        finally:
            # Always release browser instance back to pool
            await browser_pool.release(browser_instance)
            
    except Exception as e:
        processing_time = datetime.utcnow().timestamp() - processing_start
        error_message = str(e)
        
        logger.error("Query processing failed", 
                    task_id=task_id, 
                    error=error_message,
                    processing_time=processing_time,
                    exc_info=True)
        
        # Update task as failed
        await update_task_status(
            task_id,
            TaskStatus.FAILED,
            error_message=error_message,
            processing_time=processing_time
        )
        
        return {
            "status": "error",
            "error": error_message,
            "processing_time": processing_time
        }


if __name__ == "__main__":
    # Start Celery worker
    celery_app.start()
