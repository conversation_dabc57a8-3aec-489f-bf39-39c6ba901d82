"""
Browser instance pool management for concurrent processing.
"""

import asyncio
import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Playwright
import structlog

logger = structlog.get_logger(__name__)


class BrowserInstanceStatus(str, Enum):
    """Browser instance status enumeration."""
    IDLE = "idle"
    BUSY = "busy"
    CRASHED = "crashed"
    TERMINATED = "terminated"


@dataclass
class BrowserInstance:
    """Browser instance container."""
    id: str
    browser: Browser
    context: BrowserContext
    page: Page
    status: BrowserInstanceStatus
    created_at: datetime
    last_used: datetime
    task_count: int = 0
    error_count: int = 0
    current_task_id: Optional[str] = None


class BrowserPool:
    """
    Browser instance pool for managing concurrent browser automation.
    
    This class manages a pool of browser instances to handle multiple
    concurrent requests efficiently while avoiding resource exhaustion.
    """
    
    def __init__(
        self,
        pool_size: int = 5,
        browser_timeout: int = 60,
        headless: bool = True,
        max_task_per_instance: int = 100,
        instance_lifetime: int = 3600  # 1 hour
    ):
        self.pool_size = pool_size
        self.browser_timeout = browser_timeout
        self.headless = headless
        self.max_task_per_instance = max_task_per_instance
        self.instance_lifetime = instance_lifetime
        
        self.playwright: Optional[Playwright] = None
        self.instances: List[BrowserInstance] = []
        self.acquisition_queue: asyncio.Queue = asyncio.Queue()
        self.lock = asyncio.Lock()
        
        # Statistics
        self.total_acquisitions = 0
        self.total_releases = 0
        self.total_crashes = 0
        
        logger.info("Browser pool initialized", 
                   pool_size=pool_size, 
                   headless=headless,
                   max_task_per_instance=max_task_per_instance)
    
    async def initialize(self):
        """Initialize the browser pool."""
        try:
            logger.info("Starting browser pool initialization")
            
            # Start Playwright
            self.playwright = await async_playwright().start()
            
            # Create initial browser instances
            for i in range(self.pool_size):
                instance = await self._create_browser_instance()
                if instance:
                    self.instances.append(instance)
                    logger.info("Browser instance created", instance_id=instance.id, index=i+1)
                else:
                    logger.error("Failed to create browser instance", index=i+1)
            
            logger.info("Browser pool initialization completed", 
                       instances_created=len(self.instances))
            
            # Start background maintenance task
            asyncio.create_task(self._maintenance_loop())
            
        except Exception as e:
            logger.error("Failed to initialize browser pool", error=str(e), exc_info=True)
            await self.cleanup()
            raise
    
    async def _create_browser_instance(self) -> Optional[BrowserInstance]:
        """Create a new browser instance."""
        try:
            instance_id = str(uuid.uuid4())
            
            # Launch browser
            browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=[
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-accelerated-2d-canvas",
                    "--no-first-run",
                    "--no-zygote",
                    "--disable-gpu",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                    "--disable-features=TranslateUI",
                    "--disable-ipc-flooding-protection"
                ]
            )
            
            # Create context
            context = await browser.new_context(
                viewport={"width": 1920, "height": 1080},
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            
            # Create page
            page = await context.new_page()
            page.set_default_timeout(self.browser_timeout * 1000)
            
            # Create instance
            instance = BrowserInstance(
                id=instance_id,
                browser=browser,
                context=context,
                page=page,
                status=BrowserInstanceStatus.IDLE,
                created_at=datetime.utcnow(),
                last_used=datetime.utcnow()
            )
            
            return instance
            
        except Exception as e:
            logger.error("Failed to create browser instance", error=str(e), exc_info=True)
            return None
    
    async def acquire(self, timeout: int = 30) -> BrowserInstance:
        """
        Acquire a browser instance from the pool.
        
        Args:
            timeout: Maximum time to wait for an available instance
            
        Returns:
            BrowserInstance: Available browser instance
            
        Raises:
            asyncio.TimeoutError: If no instance becomes available within timeout
        """
        start_time = datetime.utcnow()
        
        try:
            logger.debug("Acquiring browser instance", timeout=timeout)
            
            while True:
                async with self.lock:
                    # Find idle instance
                    for instance in self.instances:
                        if instance.status == BrowserInstanceStatus.IDLE:
                            # Mark as busy
                            instance.status = BrowserInstanceStatus.BUSY
                            instance.last_used = datetime.utcnow()
                            instance.task_count += 1
                            
                            self.total_acquisitions += 1
                            
                            logger.debug("Browser instance acquired", 
                                        instance_id=instance.id,
                                        task_count=instance.task_count)
                            
                            return instance
                
                # Check timeout
                elapsed = (datetime.utcnow() - start_time).total_seconds()
                if elapsed >= timeout:
                    raise asyncio.TimeoutError("Timeout waiting for browser instance")
                
                # Wait a bit before retrying
                await asyncio.sleep(0.1)
                
        except Exception as e:
            logger.error("Failed to acquire browser instance", error=str(e), exc_info=True)
            raise
    
    async def release(self, instance: BrowserInstance):
        """
        Release a browser instance back to the pool.
        
        Args:
            instance: Browser instance to release
        """
        try:
            async with self.lock:
                # Check if instance should be retired
                should_retire = (
                    instance.task_count >= self.max_task_per_instance or
                    instance.error_count > 5 or
                    (datetime.utcnow() - instance.created_at).total_seconds() > self.instance_lifetime
                )
                
                if should_retire:
                    logger.info("Retiring browser instance", 
                               instance_id=instance.id,
                               task_count=instance.task_count,
                               error_count=instance.error_count)
                    
                    # Remove from pool
                    if instance in self.instances:
                        self.instances.remove(instance)
                    
                    # Clean up instance
                    await self._cleanup_instance(instance)
                    
                    # Create replacement
                    replacement = await self._create_browser_instance()
                    if replacement:
                        self.instances.append(replacement)
                        logger.info("Replacement browser instance created", 
                                   instance_id=replacement.id)
                else:
                    # Reset instance state
                    instance.status = BrowserInstanceStatus.IDLE
                    instance.current_task_id = None
                    
                    # Clear page state
                    try:
                        await instance.page.goto("about:blank")
                    except:
                        pass  # Ignore errors during cleanup
                
                self.total_releases += 1
                
                logger.debug("Browser instance released", 
                            instance_id=instance.id,
                            retired=should_retire)
                
        except Exception as e:
            logger.error("Failed to release browser instance", 
                        instance_id=instance.id, 
                        error=str(e), 
                        exc_info=True)
            
            # Mark as crashed if release fails
            instance.status = BrowserInstanceStatus.CRASHED
            instance.error_count += 1
    
    async def _cleanup_instance(self, instance: BrowserInstance):
        """Clean up a browser instance."""
        try:
            if instance.page:
                await instance.page.close()
            if instance.context:
                await instance.context.close()
            if instance.browser:
                await instance.browser.close()
                
        except Exception as e:
            logger.warning("Error cleaning up browser instance", 
                          instance_id=instance.id, 
                          error=str(e))
    
    async def _maintenance_loop(self):
        """Background maintenance loop for the browser pool."""
        while True:
            try:
                await asyncio.sleep(60)  # Run every minute
                
                async with self.lock:
                    # Check for crashed instances
                    crashed_instances = [
                        instance for instance in self.instances 
                        if instance.status == BrowserInstanceStatus.CRASHED
                    ]
                    
                    for instance in crashed_instances:
                        logger.warning("Removing crashed browser instance", 
                                      instance_id=instance.id)
                        
                        self.instances.remove(instance)
                        await self._cleanup_instance(instance)
                        self.total_crashes += 1
                        
                        # Create replacement
                        replacement = await self._create_browser_instance()
                        if replacement:
                            self.instances.append(replacement)
                    
                    # Ensure minimum pool size
                    while len(self.instances) < self.pool_size:
                        instance = await self._create_browser_instance()
                        if instance:
                            self.instances.append(instance)
                        else:
                            break  # Stop trying if creation fails
                
            except Exception as e:
                logger.error("Error in browser pool maintenance", error=str(e), exc_info=True)
    
    async def cleanup(self):
        """Clean up all browser instances and resources."""
        try:
            logger.info("Cleaning up browser pool")
            
            # Clean up all instances
            for instance in self.instances:
                await self._cleanup_instance(instance)
            
            self.instances.clear()
            
            # Stop Playwright
            if self.playwright:
                await self.playwright.stop()
            
            logger.info("Browser pool cleanup completed")
            
        except Exception as e:
            logger.error("Error during browser pool cleanup", error=str(e))
    
    def get_stats(self) -> Dict[str, Any]:
        """Get browser pool statistics."""
        idle_count = sum(1 for i in self.instances if i.status == BrowserInstanceStatus.IDLE)
        busy_count = sum(1 for i in self.instances if i.status == BrowserInstanceStatus.BUSY)
        crashed_count = sum(1 for i in self.instances if i.status == BrowserInstanceStatus.CRASHED)
        
        return {
            "pool_size": self.pool_size,
            "total_instances": len(self.instances),
            "idle_instances": idle_count,
            "busy_instances": busy_count,
            "crashed_instances": crashed_count,
            "total_acquisitions": self.total_acquisitions,
            "total_releases": self.total_releases,
            "total_crashes": self.total_crashes
        }
