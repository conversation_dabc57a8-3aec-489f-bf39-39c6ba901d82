# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_KEY_REQUIRED=true
DEFAULT_API_KEY=your-secure-api-key-here

# Database Configuration
DATABASE_URL=************************************************/perplexigate
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis Configuration
REDIS_URL=redis://redis:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# Browser Configuration
BROWSER_POOL_SIZE=5
BROWSER_TIMEOUT=60
BROWSER_NAVIGATION_TIMEOUT=30
HEADLESS_MODE=true
BROWSER_VIEWPORT_WIDTH=1920
BROWSER_VIEWPORT_HEIGHT=1080
ENABLE_SCREENSHOTS=true
SCREENSHOT_ON_ERROR=true

# Celery Configuration
CELERY_WORKERS=4
TASK_TIMEOUT=300
TASK_SOFT_TIMEOUT=240
TASK_MAX_RETRIES=3
TASK_RETRY_DELAY=60

# Perplexity Configuration
PERPLEXITY_BASE_URL=https://www.perplexity.ai
DEFAULT_SEARCH_MODE=quick
DEFAULT_FOCUS_MODE=all
ENABLE_DEEP_SEARCH=true
MAX_FOLLOW_UP_QUESTIONS=5
SESSION_TIMEOUT=3600

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10
RATE_LIMIT_STORAGE=redis

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_PATH=/app/logs/perplexigate.log
LOG_MAX_SIZE=100MB
LOG_BACKUP_COUNT=5

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9000
PROMETHEUS_MULTIPROC_DIR=/tmp/prometheus_multiproc_dir

# Security Configuration
SECRET_KEY=your-secret-key-here
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
ALLOWED_HOSTS=["localhost", "127.0.0.1"]

# Performance Configuration
MAX_CONCURRENT_REQUESTS=50
REQUEST_TIMEOUT=300
CONNECTION_POOL_SIZE=100
KEEP_ALIVE_TIMEOUT=5

# Feature Flags
ENABLE_CACHING=true
CACHE_TTL=300
ENABLE_REQUEST_VALIDATION=true
ENABLE_RESPONSE_COMPRESSION=true

# Development Configuration
DEBUG=false
RELOAD=false
TESTING=false

# Docker Configuration
COMPOSE_PROJECT_NAME=perplexigate
COMPOSE_FILE=docker-compose.yml
