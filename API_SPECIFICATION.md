# PerplexiGate API Specification

## Overview

PerplexiGate provides a RESTful API for automating Perplexity AI web interactions. The API enables programmatic access to Perplexity's search capabilities including Pro search, Focus modes, and Deep Research functionality.

## Base URL

```
Production: https://api.perplexigate.com
Development: http://localhost:8000
```

## Authentication

All API requests require authentication using API keys passed in the `Authorization` header:

```http
Authorization: Bearer YOUR_API_KEY
```

## Rate Limiting

- **Default**: 60 requests per minute per API key
- **Burst**: 10 requests per second
- **Headers**: Rate limit information is returned in response headers

```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1640995200
```

## Endpoints

### Query Processing

#### Submit Query

Submit a query for processing by Perplexity AI.

```http
POST /api/v1/query
```

**Request Body:**
```json
{
  "query": "What are the latest developments in quantum computing?",
  "search_mode": "pro",
  "focus_mode": "academic",
  "deep_search": true,
  "sources": ["academic", "news"],
  "session_id": "optional-session-id"
}
```

**Parameters:**
- `query` (string, required): Search query (1-2000 characters)
- `search_mode` (string, optional): Search mode - `quick`, `pro`, `focus` (default: `quick`)
- `focus_mode` (string, optional): Focus mode - `all`, `academic`, `writing`, `wolfram`, `youtube`, `reddit`, `news` (default: `all`)
- `deep_search` (boolean, optional): Enable deep search (default: `false`)
- `sources` (array, optional): Source filters
- `session_id` (string, optional): Session ID for conversation context

**Response:**
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "status": "pending",
  "progress": 0,
  "response": null,
  "error": null,
  "metadata": {
    "processing_time": null,
    "session_id": "session-uuid",
    "timestamp": "2024-01-01T12:00:00Z",
    "retry_count": 0
  }
}
```

#### Get Task Status

Retrieve the status and results of a submitted query.

```http
GET /api/v1/query/{task_id}
```

**Response:**
```json
{
  "task_id": "550e8400-e29b-41d4-a716-************",
  "status": "completed",
  "progress": 100,
  "response": {
    "answer": "Quantum computing has seen significant developments...",
    "citations": [
      {
        "title": "Quantum Computing Breakthrough",
        "url": "https://example.com/article",
        "snippet": "Recent advances in quantum error correction...",
        "domain": "example.com",
        "published_date": "2024-01-01T10:00:00Z",
        "source_type": "academic"
      }
    ],
    "sources": ["academic", "news"],
    "search_mode": "pro",
    "focus_mode": "academic",
    "deep_search_used": true,
    "follow_up_suggestions": [
      "What are the main challenges in quantum error correction?",
      "How do quantum computers compare to classical computers?"
    ]
  },
  "error": null,
  "metadata": {
    "processing_time": 15.2,
    "session_id": "session-uuid",
    "timestamp": "2024-01-01T12:00:15Z",
    "retry_count": 0
  }
}
```

#### Cancel Task

Cancel a pending or processing task.

```http
DELETE /api/v1/query/{task_id}
```

**Response:**
```json
{
  "message": "Task cancelled successfully"
}
```

#### List Tasks

List tasks with optional filtering and pagination.

```http
GET /api/v1/tasks?page=1&size=20&status=completed&session_id=session-uuid
```

**Query Parameters:**
- `page` (integer, optional): Page number (default: 1)
- `size` (integer, optional): Page size (1-100, default: 20)
- `status` (string, optional): Filter by status
- `session_id` (string, optional): Filter by session

**Response:**
```json
{
  "items": [...],
  "total": 150,
  "page": 1,
  "size": 20,
  "pages": 8,
  "has_next": true,
  "has_prev": false
}
```

### Session Management

#### Create Session

Create a new session for conversation context.

```http
POST /api/v1/sessions
```

**Request Body:**
```json
{
  "title": "Quantum Computing Research",
  "default_search_mode": "pro",
  "default_focus_mode": "academic",
  "default_deep_search": true
}
```

**Response:**
```json
{
  "session_id": "550e8400-e29b-41d4-a716-************",
  "title": "Quantum Computing Research",
  "is_active": true,
  "created_at": "2024-01-01T12:00:00Z",
  "last_activity": "2024-01-01T12:00:00Z",
  "expires_at": "2024-01-02T12:00:00Z",
  "conversation_count": 0
}
```

#### Get Session

Retrieve session information.

```http
GET /api/v1/sessions/{session_id}
```

#### Delete Session

Delete a session and its conversation history.

```http
DELETE /api/v1/sessions/{session_id}
```

#### List Sessions

List sessions with pagination.

```http
GET /api/v1/sessions?page=1&size=20&active_only=true
```

### Health and Monitoring

#### Health Check

Get comprehensive system health status.

```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2024-01-01T12:00:00Z",
  "components": {
    "database": "healthy",
    "redis": "healthy",
    "celery": "healthy",
    "api": "healthy"
  },
  "uptime": 86400.0
}
```

#### Readiness Check

Kubernetes readiness probe endpoint.

```http
GET /health/ready
```

#### Liveness Check

Kubernetes liveness probe endpoint.

```http
GET /health/live
```

#### Metrics

Get basic system metrics.

```http
GET /health/metrics
```

## Status Codes

- `200` - Success
- `201` - Created
- `202` - Accepted (async operation started)
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Rate Limited
- `500` - Internal Server Error
- `503` - Service Unavailable

## Error Response Format

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    "field": "Additional error details"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Task Status Values

- `pending` - Task is queued for processing
- `processing` - Task is currently being processed
- `completed` - Task completed successfully
- `failed` - Task failed with error
- `cancelled` - Task was cancelled
- `retrying` - Task is being retried after failure

## Search Modes

- `quick` - Fast search with basic results
- `pro` - Enhanced search with more comprehensive results
- `focus` - Focused search with specific source types

## Focus Modes

- `all` - Search all available sources
- `academic` - Focus on academic and research sources
- `writing` - Focus on writing and creative content
- `wolfram` - Focus on computational and mathematical content
- `youtube` - Focus on video content
- `reddit` - Focus on Reddit discussions
- `news` - Focus on news and current events

## Source Types

- `academic` - Academic papers and research
- `news` - News articles and journalism
- `reddit` - Reddit posts and discussions
- `youtube` - YouTube videos
- `writing` - Writing and creative content
- `wolfram` - Computational results
- `web` - General web content

## Webhooks (Future Feature)

Configure webhooks to receive notifications when tasks complete:

```json
{
  "webhook_url": "https://your-app.com/webhook",
  "events": ["task.completed", "task.failed"],
  "secret": "webhook-secret"
}
```

## SDKs and Libraries

Official SDKs are available for:
- Python: `pip install perplexigate-python`
- JavaScript/Node.js: `npm install perplexigate-js`
- Go: `go get github.com/perplexigate/go-sdk`

## Examples

### Python Example

```python
import perplexigate

client = perplexigate.Client(api_key="your-api-key")

# Submit query
task = client.submit_query(
    query="What is quantum entanglement?",
    search_mode="pro",
    focus_mode="academic"
)

# Wait for completion
result = client.wait_for_completion(task.task_id, timeout=60)
print(result.response.answer)
```

### JavaScript Example

```javascript
const PerplexiGate = require('perplexigate-js');

const client = new PerplexiGate({ apiKey: 'your-api-key' });

async function searchQuery() {
  const task = await client.submitQuery({
    query: 'What is quantum entanglement?',
    searchMode: 'pro',
    focusMode: 'academic'
  });
  
  const result = await client.waitForCompletion(task.taskId);
  console.log(result.response.answer);
}
```

### cURL Example

```bash
# Submit query
curl -X POST "http://localhost:8000/api/v1/query" \
  -H "Authorization: Bearer your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is quantum entanglement?",
    "search_mode": "pro",
    "focus_mode": "academic"
  }'

# Check status
curl -X GET "http://localhost:8000/api/v1/query/task-id" \
  -H "Authorization: Bearer your-api-key"
```
