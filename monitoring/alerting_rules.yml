groups:
  - name: perplexigate_alerts
    rules:
      # High error rate alert
      - alert: HighErrorRate
        expr: perplexigate:error_rate_5m > 0.1
        for: 2m
        labels:
          severity: warning
          service: perplexigate
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
      
      # Critical error rate alert
      - alert: CriticalErrorRate
        expr: perplexigate:error_rate_5m > 0.25
        for: 1m
        labels:
          severity: critical
          service: perplexigate
        annotations:
          summary: "Critical error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
      
      # High response time alert
      - alert: HighResponseTime
        expr: perplexigate:response_time_p95_5m > 30
        for: 5m
        labels:
          severity: warning
          service: perplexigate
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for the last 5 minutes"
      
      # Service down alert
      - alert: ServiceDown
        expr: up{job="perplexigate-api"} == 0
        for: 1m
        labels:
          severity: critical
          service: perplexigate
        annotations:
          summary: "PerplexiGate API is down"
          description: "PerplexiGate API has been down for more than 1 minute"
      
      # Database connectivity alert
      - alert: DatabaseDown
        expr: perplexigate_database_health == 0
        for: 1m
        labels:
          severity: critical
          service: perplexigate
        annotations:
          summary: "Database connectivity lost"
          description: "Cannot connect to the database"
      
      # Redis connectivity alert
      - alert: RedisDown
        expr: perplexigate_redis_health == 0
        for: 1m
        labels:
          severity: critical
          service: perplexigate
        annotations:
          summary: "Redis connectivity lost"
          description: "Cannot connect to Redis"
      
      # No healthy workers alert
      - alert: NoHealthyWorkers
        expr: perplexigate_celery_workers_healthy == 0
        for: 2m
        labels:
          severity: critical
          service: perplexigate
        annotations:
          summary: "No healthy Celery workers"
          description: "All Celery workers are unhealthy or unavailable"
      
      # High queue depth alert
      - alert: HighQueueDepth
        expr: perplexigate_queue_size > 100
        for: 5m
        labels:
          severity: warning
          service: perplexigate
        annotations:
          summary: "High queue depth"
          description: "Queue depth is {{ $value }} tasks"
      
      # Browser pool exhaustion alert
      - alert: BrowserPoolExhausted
        expr: perplexigate:browser_utilization > 0.9
        for: 3m
        labels:
          severity: warning
          service: perplexigate
        annotations:
          summary: "Browser pool nearly exhausted"
          description: "Browser pool utilization is {{ $value | humanizePercentage }}"
      
      # Task failure rate alert
      - alert: HighTaskFailureRate
        expr: perplexigate:task_failure_rate_5m / perplexigate:task_completion_rate_5m > 0.2
        for: 5m
        labels:
          severity: warning
          service: perplexigate
        annotations:
          summary: "High task failure rate"
          description: "Task failure rate is {{ $value | humanizePercentage }} for the last 5 minutes"

  - name: system_alerts
    rules:
      # High CPU usage
      - alert: HighCPUUsage
        expr: instance:cpu_utilization > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"
      
      # High memory usage
      - alert: HighMemoryUsage
        expr: instance:memory_utilization > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}"
      
      # High disk usage
      - alert: HighDiskUsage
        expr: instance:disk_utilization > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High disk usage"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }}"
