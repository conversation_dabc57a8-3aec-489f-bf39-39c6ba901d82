groups:
  - name: perplexigate_recording_rules
    interval: 30s
    rules:
      # Request rate calculations
      - record: perplexigate:request_rate_5m
        expr: rate(http_requests_total[5m])
      
      - record: perplexigate:request_rate_1h
        expr: rate(http_requests_total[1h])
      
      # Error rate calculations
      - record: perplexigate:error_rate_5m
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])
      
      - record: perplexigate:error_rate_1h
        expr: rate(http_requests_total{status=~"5.."}[1h]) / rate(http_requests_total[1h])
      
      # Response time percentiles
      - record: perplexigate:response_time_p95_5m
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))
      
      - record: perplexigate:response_time_p99_5m
        expr: histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))
      
      # Task processing metrics
      - record: perplexigate:task_completion_rate_5m
        expr: rate(perplexigate_tasks_completed_total[5m])
      
      - record: perplexigate:task_failure_rate_5m
        expr: rate(perplexigate_tasks_failed_total[5m])
      
      - record: perplexigate:task_success_rate_5m
        expr: perplexigate:task_completion_rate_5m / (perplexigate:task_completion_rate_5m + perplexigate:task_failure_rate_5m)
      
      # Browser pool metrics
      - record: perplexigate:browser_utilization
        expr: perplexigate_browser_instances_busy / perplexigate_browser_instances_total
      
      # Queue metrics
      - record: perplexigate:queue_depth_avg_5m
        expr: avg_over_time(perplexigate_queue_size[5m])
      
      - record: perplexigate:queue_processing_rate_5m
        expr: rate(perplexigate_queue_processed_total[5m])

  - name: system_recording_rules
    interval: 30s
    rules:
      # CPU utilization
      - record: instance:cpu_utilization
        expr: 100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
      
      # Memory utilization
      - record: instance:memory_utilization
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100
      
      # Disk utilization
      - record: instance:disk_utilization
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100
