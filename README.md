# PerplexiGate - AI Agent for Perplexity Web Automation

An intelligent automation system that provides API access to Perplexity AI's web interface, enabling programmatic search queries with support for all Perplexity features including Pro search, Focus modes, and Deep Research.

## 🏗️ System Architecture

PerplexiGate is built using a microservices architecture with the following components:

- **API Gateway**: FastAPI-based REST API for request handling
- **Browser Automation**: Playwright-powered web automation service
- **Queue Management**: Redis + Celery for concurrent request processing
- **Session Management**: Multi-browser instance management
- **Response Processing**: Citation extraction and response formatting
- **Monitoring**: Comprehensive logging and metrics collection

## 🚀 Features

### Core Functionality
- ✅ RESTful API for query submission
- ✅ Automated Perplexity web interface interaction
- ✅ Concurrent processing of multiple requests
- ✅ Session management for follow-up questions
- ✅ Formatted response extraction with citations

### Perplexity Feature Support
- ✅ Search modes (Quick, Pro, Focus)
- ✅ Deep Search functionality
- ✅ Source selection (Academic, Reddit, YouTube, etc.)
- ✅ Citation and source link extraction
- ✅ Conversation context preservation
- ✅ Follow-up question support

### Technical Features
- 🐳 Containerized deployment with Docker
- 📊 Real-time monitoring and metrics
- 🔄 Automatic retry mechanisms
- 🛡️ Rate limiting and authentication
- 📝 Comprehensive logging
- 🎯 Load balancing and scaling

## 📋 Requirements

- Docker & Docker Compose
- Python 3.11+
- Redis
- PostgreSQL
- Minimum 4GB RAM (8GB recommended)
- 2+ CPU cores

## 🛠️ Technology Stack

| Component | Technology | Purpose |
|-----------|------------|---------|
| API Framework | FastAPI | REST API endpoints |
| Web Automation | Playwright | Browser control |
| Task Queue | Celery + Redis | Async task processing |
| Database | PostgreSQL | Session & log storage |
| Monitoring | Prometheus + Grafana | Metrics & dashboards |
| Containerization | Docker | Deployment |

## 🚀 Quick Start

1. **Clone the repository**
```bash
git clone <repository-url>
cd BellingByte-PerplexiGate
```

2. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start services**
```bash
docker-compose up -d
```

4. **Verify installation**
```bash
curl http://localhost:8000/health
```

## 📖 API Documentation

### Submit Query
```http
POST /api/v1/query
Content-Type: application/json

{
  "query": "What are the latest developments in quantum computing?",
  "search_mode": "pro",
  "focus_mode": "academic",
  "deep_search": true,
  "sources": ["academic", "reddit"],
  "session_id": "optional-session-id"
}
```

### Response Format
```json
{
  "task_id": "uuid-task-id",
  "status": "completed",
  "response": {
    "answer": "Formatted response text...",
    "citations": [
      {
        "title": "Source Title",
        "url": "https://source-url.com",
        "snippet": "Relevant excerpt..."
      }
    ],
    "sources": ["academic", "news"],
    "search_mode": "pro",
    "deep_search_used": true
  },
  "metadata": {
    "processing_time": 15.2,
    "session_id": "session-uuid",
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

## 🔧 Configuration

Key configuration options in `.env`:

```env
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# Browser Configuration
BROWSER_POOL_SIZE=5
BROWSER_TIMEOUT=30
HEADLESS_MODE=true

# Queue Configuration
REDIS_URL=redis://redis:6379/0
CELERY_WORKERS=4
TASK_TIMEOUT=300

# Database
DATABASE_URL=************************************/perplexigate

# Monitoring
ENABLE_METRICS=true
LOG_LEVEL=INFO
```

## 📊 Monitoring

Access monitoring dashboards:
- **API Docs**: http://localhost:8000/docs
- **Task Monitor**: http://localhost:5555 (Flower)
- **Metrics**: http://localhost:9090 (Prometheus)
- **Dashboard**: http://localhost:3000 (Grafana)

## 🔒 Security

- API key authentication
- Rate limiting per client
- Request validation
- Secure browser contexts
- No credential storage

## 🧪 Testing

```bash
# Run unit tests
docker-compose exec api pytest tests/

# Run integration tests
docker-compose exec api pytest tests/integration/

# Load testing
docker-compose exec api pytest tests/load/
```

## 📈 Scaling

### Horizontal Scaling
- Increase Celery workers: `docker-compose up --scale worker=8`
- Add API replicas: `docker-compose up --scale api=3`
- Browser pool scaling: Adjust `BROWSER_POOL_SIZE`

### Performance Tuning
- Browser instance reuse
- Connection pooling
- Response caching
- Queue optimization

## 🐛 Troubleshooting

Common issues and solutions:

1. **Browser crashes**: Increase memory limits
2. **Slow responses**: Tune browser pool size
3. **Queue backlog**: Scale workers
4. **Rate limiting**: Implement delays

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Add tests
4. Submit pull request

## 📞 Support

- Documentation: `/docs`
- Issues: GitHub Issues
- Discussions: GitHub Discussions
