-- Initial database setup for PerplexiGate
-- This file is executed when the PostgreSQL container starts

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create indexes for better performance
-- These will be created after tables are created by SQLAlchemy

-- Function to create indexes after table creation
CREATE OR REPLACE FUNCTION create_perplexigate_indexes()
R<PERSON><PERSON>NS void AS $$
BEGIN
    -- Check if tables exist before creating indexes
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'query_tasks') THEN
        -- Indexes for query_tasks table
        CREATE INDEX IF NOT EXISTS idx_query_tasks_task_id ON query_tasks(task_id);
        CREATE INDEX IF NOT EXISTS idx_query_tasks_status ON query_tasks(status);
        CREATE INDEX IF NOT EXISTS idx_query_tasks_session_id ON query_tasks(session_id);
        CREATE INDEX IF NOT EXISTS idx_query_tasks_created_at ON query_tasks(created_at);
        CREATE INDEX IF NOT EXISTS idx_query_tasks_status_created ON query_tasks(status, created_at);
        
        -- Composite index for common queries
        CREATE INDEX IF NOT EXISTS idx_query_tasks_status_session ON query_tasks(status, session_id);
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'sessions') THEN
        -- Indexes for sessions table
        CREATE INDEX IF NOT EXISTS idx_sessions_session_id ON sessions(session_id);
        CREATE INDEX IF NOT EXISTS idx_sessions_active ON sessions(is_active);
        CREATE INDEX IF NOT EXISTS idx_sessions_last_activity ON sessions(last_activity);
        CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON sessions(expires_at);
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'browser_instances') THEN
        -- Indexes for browser_instances table
        CREATE INDEX IF NOT EXISTS idx_browser_instances_instance_id ON browser_instances(instance_id);
        CREATE INDEX IF NOT EXISTS idx_browser_instances_status ON browser_instances(status);
        CREATE INDEX IF NOT EXISTS idx_browser_instances_worker_id ON browser_instances(worker_id);
        CREATE INDEX IF NOT EXISTS idx_browser_instances_last_activity ON browser_instances(last_activity);
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'api_keys') THEN
        -- Indexes for api_keys table
        CREATE INDEX IF NOT EXISTS idx_api_keys_key_hash ON api_keys(key_hash);
        CREATE INDEX IF NOT EXISTS idx_api_keys_active ON api_keys(is_active);
        CREATE INDEX IF NOT EXISTS idx_api_keys_last_used ON api_keys(last_used);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create a function to clean up old data
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- Clean up old completed tasks (older than 30 days)
    DELETE FROM query_tasks 
    WHERE status IN ('completed', 'failed', 'cancelled') 
    AND completed_at < NOW() - INTERVAL '30 days';
    
    -- Clean up old inactive sessions (older than 7 days)
    DELETE FROM sessions 
    WHERE is_active = false 
    AND last_activity < NOW() - INTERVAL '7 days';
    
    -- Clean up old terminated browser instances (older than 1 day)
    DELETE FROM browser_instances 
    WHERE status = 'terminated' 
    AND terminated_at < NOW() - INTERVAL '1 day';
    
    -- Log cleanup
    RAISE NOTICE 'Cleanup completed at %', NOW();
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job for cleanup (requires pg_cron extension)
-- This is commented out as pg_cron might not be available in all environments
-- SELECT cron.schedule('cleanup-old-data', '0 2 * * *', 'SELECT cleanup_old_data();');

-- Create views for monitoring and analytics
CREATE OR REPLACE VIEW task_statistics AS
SELECT 
    status,
    COUNT(*) as count,
    AVG(processing_time) as avg_processing_time,
    MIN(processing_time) as min_processing_time,
    MAX(processing_time) as max_processing_time,
    DATE_TRUNC('hour', created_at) as hour
FROM query_tasks 
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY status, DATE_TRUNC('hour', created_at)
ORDER BY hour DESC;

CREATE OR REPLACE VIEW session_statistics AS
SELECT 
    COUNT(*) as total_sessions,
    COUNT(CASE WHEN is_active THEN 1 END) as active_sessions,
    AVG(EXTRACT(EPOCH FROM (last_activity - created_at))) as avg_session_duration,
    DATE_TRUNC('hour', created_at) as hour
FROM sessions 
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY DATE_TRUNC('hour', created_at)
ORDER BY hour DESC;

CREATE OR REPLACE VIEW browser_pool_statistics AS
SELECT 
    status,
    COUNT(*) as count,
    AVG(total_tasks) as avg_tasks_per_instance,
    AVG(successful_tasks::float / NULLIF(total_tasks, 0)) as success_rate
FROM browser_instances 
GROUP BY status;

-- Grant permissions
GRANT SELECT ON task_statistics TO perplexigate;
GRANT SELECT ON session_statistics TO perplexigate;
GRANT SELECT ON browser_pool_statistics TO perplexigate;
GRANT EXECUTE ON FUNCTION cleanup_old_data() TO perplexigate;
GRANT EXECUTE ON FUNCTION create_perplexigate_indexes() TO perplexigate;
